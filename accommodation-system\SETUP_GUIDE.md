# 住宿管理系统安装配置指南

## 第一步：环境准备

### Windows 系统
1. 运行 `setup.bat` 自动下载和配置 PocketBase
2. 或手动下载：
   ```bash
   curl -L https://github.com/pocketbase/pocketbase/releases/latest/download/pocketbase_windows_amd64.zip -o pocketbase.zip
   ```

### macOS/Linux 系统
```bash
# macOS
curl -L https://github.com/pocketbase/pocketbase/releases/latest/download/pocketbase_darwin_amd64.zip -o pocketbase.zip

# Linux
curl -L https://github.com/pocketbase/pocketbase/releases/latest/download/pocketbase_linux_amd64.zip -o pocketbase.zip

# 解压
unzip pocketbase.zip
chmod +x pocketbase
```

## 第二步：启动服务

### Windows
```bash
# 使用批处理文件
start.bat

# 或直接运行
pocketbase.exe serve
```

### macOS/Linux
```bash
./pocketbase serve
```

服务启动后访问：
- 管理后台：http://127.0.0.1:8090/_/
- 应用前端：http://127.0.0.1:8090/

## 第三步：初始化配置

### 1. 创建管理员账户
首次访问管理后台时，系统会提示创建管理员账户：
- 邮箱：<EMAIL>
- 密码：设置一个强密码

### 2. 创建数据集合

在管理后台的 "Collections" 页面，点击 "New collection" 创建以下集合：

#### 2.1 rooms 集合
```json
{
  "name": "rooms",
  "type": "base",
  "schema": [
    {"name": "room_number", "type": "text", "required": true, "unique": true},
    {"name": "room_type", "type": "select", "required": true, "options": {"values": ["single", "double", "suite", "family"]}},
    {"name": "capacity", "type": "number", "required": true},
    {"name": "price", "type": "number", "required": true},
    {"name": "status", "type": "select", "required": true, "options": {"values": ["available", "occupied", "maintenance", "reserved"]}},
    {"name": "description", "type": "text"},
    {"name": "amenities", "type": "json"},
    {"name": "floor", "type": "number"},
    {"name": "images", "type": "file", "options": {"maxSelect": 5, "maxSize": 5242880}}
  ]
}
```

#### 2.2 bookings 集合
```json
{
  "name": "bookings",
  "type": "base",
  "schema": [
    {"name": "user", "type": "relation", "required": true, "options": {"collectionId": "_pb_users_auth_"}},
    {"name": "room", "type": "relation", "required": true, "options": {"collectionId": "rooms"}},
    {"name": "check_in", "type": "date", "required": true},
    {"name": "check_out", "type": "date", "required": true},
    {"name": "guests", "type": "number", "required": true},
    {"name": "guest_names", "type": "json"},
    {"name": "purpose", "type": "select", "required": true, "options": {"values": ["business", "training", "visitor", "other"]}},
    {"name": "status", "type": "select", "required": true, "options": {"values": ["pending", "confirmed", "checked_in", "checked_out", "cancelled"]}},
    {"name": "total_amount", "type": "number"},
    {"name": "paid_amount", "type": "number"},
    {"name": "notes", "type": "text"},
    {"name": "approved_by", "type": "relation", "options": {"collectionId": "_pb_users_auth_"}},
    {"name": "approved_at", "type": "date"}
  ]
}
```

#### 2.3 payments 集合
```json
{
  "name": "payments",
  "type": "base",
  "schema": [
    {"name": "booking", "type": "relation", "required": true, "options": {"collectionId": "bookings"}},
    {"name": "amount", "type": "number", "required": true},
    {"name": "payment_date", "type": "date", "required": true},
    {"name": "payment_method", "type": "select", "required": true, "options": {"values": ["cash", "card", "transfer", "deduction"]}},
    {"name": "status", "type": "select", "required": true, "options": {"values": ["pending", "completed", "failed", "refunded"]}},
    {"name": "transaction_id", "type": "text"},
    {"name": "notes", "type": "text"},
    {"name": "processed_by", "type": "relation", "options": {"collectionId": "_pb_users_auth_"}}
  ]
}
```

### 3. 配置用户集合字段

在 "Auth collections" 中找到 "users" 集合，添加以下字段：
- `name` (text) - 真实姓名
- `employee_id` (text, unique) - 员工编号
- `department` (text) - 部门
- `position` (text) - 职位
- `phone` (text) - 电话号码
- `role` (select) - 角色：admin, manager, employee
- `is_active` (bool) - 账户状态

### 4. 设置权限规则

#### rooms 集合权限
- List/Search: `@request.auth.id != ""`
- View: `@request.auth.id != ""`
- Create: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- Update: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- Delete: `@request.auth.role = "admin"`

#### bookings 集合权限
- List/Search: `@request.auth.id = user || @request.auth.role = "admin" || @request.auth.role = "manager"`
- View: `@request.auth.id = user || @request.auth.role = "admin" || @request.auth.role = "manager"`
- Create: `@request.auth.id != ""`
- Update: `(@request.auth.id = user && status = "pending") || @request.auth.role = "admin" || @request.auth.role = "manager"`
- Delete: `@request.auth.role = "admin"`

#### payments 集合权限
- List/Search: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- View: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- Create: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- Update: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- Delete: `@request.auth.role = "admin"`

## 第四步：添加测试数据

### 1. 创建测试用户
在管理后台的 "Auth collections" > "users" 中创建：

**管理员用户：**
- Username: admin
- Email: <EMAIL>
- Password: admin123
- Name: 系统管理员
- Role: admin

**经理用户：**
- Username: manager
- Email: <EMAIL>
- Password: manager123
- Name: 部门经理
- Role: manager

**普通员工：**
- Username: employee
- Email: <EMAIL>
- Password: employee123
- Name: 普通员工
- Role: employee

### 2. 添加测试房间
在 "Collections" > "rooms" 中添加：

```json
[
  {
    "room_number": "101",
    "room_type": "single",
    "capacity": 1,
    "price": 200,
    "status": "available",
    "description": "标准单人间，配备基础设施",
    "floor": 1
  },
  {
    "room_number": "102",
    "room_type": "double",
    "capacity": 2,
    "price": 300,
    "status": "available",
    "description": "舒适双人间，适合商务出差",
    "floor": 1
  },
  {
    "room_number": "201",
    "room_type": "suite",
    "capacity": 3,
    "price": 500,
    "status": "available",
    "description": "豪华套房，配备客厅和办公区",
    "floor": 2
  }
]
```

## 第五步：测试系统功能

1. 访问 http://127.0.0.1:8090/
2. 使用测试账户登录
3. 测试各项功能：
   - 房间浏览和筛选
   - 预订创建和管理
   - 用户权限控制
   - 数据统计显示

## 第六步：生产环境部署

### 1. 服务器部署
```bash
# 上传文件到服务器
scp -r accommodation-system/ user@server:/path/to/app/

# 在服务器上启动
./pocketbase serve --http=0.0.0.0:8090
```

### 2. 使用 systemd 管理服务
创建 `/etc/systemd/system/accommodation.service`：
```ini
[Unit]
Description=Accommodation Management System
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/app/accommodation-system
ExecStart=/path/to/app/accommodation-system/pocketbase serve --http=0.0.0.0:8090
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable accommodation
sudo systemctl start accommodation
```

### 3. 配置反向代理 (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8090;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 故障排除

### 常见问题
1. **端口被占用**：修改启动命令中的端口号
2. **权限错误**：检查文件权限和用户权限设置
3. **数据库错误**：检查 pb_data 目录权限
4. **登录失败**：确认用户账户和权限配置

### 日志查看
PocketBase 会在控制台输出日志，生产环境建议重定向到文件：
```bash
./pocketbase serve > app.log 2>&1 &
```

## 技术支持

如遇到问题，请检查：
1. PocketBase 官方文档：https://pocketbase.io/docs/
2. 系统日志和错误信息
3. 浏览器开发者工具的控制台输出
