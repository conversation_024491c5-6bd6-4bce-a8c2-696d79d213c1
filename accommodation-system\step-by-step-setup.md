# 🔧 分步创建集合指南

由于导入可能遇到格式问题，我们采用手动创建的方式，这样更稳定可靠。

## 第一步：配置用户集合

1. 访问管理后台：http://127.0.0.1:8090/_/
2. 点击左侧 **"Auth collections"**
3. 点击 **"users"** 集合
4. 点击 **"Fields"** 标签页
5. 点击 **"New field"** 按钮，依次添加以下字段：

### 添加用户字段：

**字段 1: name**
- Type: `Text`
- Name: `name`
- Label: `真实姓名`
- Max length: `100`

**字段 2: employee_id**
- Type: `Text`
- Name: `employee_id`
- Label: `员工编号`
- ✅ Unique
- Max length: `20`

**字段 3: department**
- Type: `Text`
- Name: `department`
- Label: `部门`
- Max length: `100`

**字段 4: position**
- Type: `Text`
- Name: `position`
- Label: `职位`
- Max length: `100`

**字段 5: phone**
- Type: `Text`
- Name: `phone`
- Label: `电话号码`
- Max length: `20`

**字段 6: role**
- Type: `Select (single)`
- Name: `role`
- Label: `角色`
- ✅ Required
- Values: `admin`, `manager`, `employee`

**字段 7: is_active**
- Type: `Bool`
- Name: `is_active`
- Label: `账户状态`
- Default: `true`

## 第二步：创建 rooms 集合

1. 点击左侧 **"Collections"**
2. 点击 **"New collection"**
3. 填写：
   - Name: `rooms`
   - Type: `Base collection`
4. 点击 **"Create"**

### 添加 rooms 字段：

**字段 1: room_number**
- Type: `Text`
- Name: `room_number`
- Label: `房间号`
- ✅ Required
- ✅ Unique
- Max length: `10`

**字段 2: room_type**
- Type: `Select (single)`
- Name: `room_type`
- Label: `房型`
- ✅ Required
- Values: `single`, `double`, `suite`, `family`

**字段 3: capacity**
- Type: `Number`
- Name: `capacity`
- Label: `容纳人数`
- ✅ Required
- Min: `1`, Max: `10`

**字段 4: price**
- Type: `Number`
- Name: `price`
- Label: `每晚价格`
- ✅ Required
- Min: `0`

**字段 5: status**
- Type: `Select (single)`
- Name: `status`
- Label: `状态`
- ✅ Required
- Values: `available`, `occupied`, `maintenance`, `reserved`

**字段 6: description**
- Type: `Text`
- Name: `description`
- Label: `房间描述`
- Max length: `500`

**字段 7: amenities**
- Type: `JSON`
- Name: `amenities`
- Label: `设施列表`

**字段 8: floor**
- Type: `Number`
- Name: `floor`
- Label: `楼层`
- Min: `1`, Max: `50`

**字段 9: images**
- Type: `File`
- Name: `images`
- Label: `房间图片`
- Max select: `5`
- Max size: `5242880` (5MB)
- Mime types: `image/jpeg`, `image/png`, `image/webp`

### 设置 rooms 权限规则：

点击 **"API Rules"** 标签页，设置：

- **List/Search rule**: 
  ```
  @request.auth.id != ""
  ```

- **View rule**: 
  ```
  @request.auth.id != ""
  ```

- **Create rule**: 
  ```
  @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **Update rule**: 
  ```
  @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **Delete rule**: 
  ```
  @request.auth.role = "admin"
  ```

## 第三步：创建 bookings 集合

1. 点击 **"New collection"**
2. 填写：
   - Name: `bookings`
   - Type: `Base collection`
3. 点击 **"Create"**

### 添加 bookings 字段：

**字段 1: user**
- Type: `Relation (single)`
- Name: `user`
- Label: `预订用户`
- ✅ Required
- Collection: `users`
- Display fields: `name`, `username`

**字段 2: room**
- Type: `Relation (single)`
- Name: `room`
- Label: `预订房间`
- ✅ Required
- Collection: `rooms`
- Display fields: `room_number`, `room_type`

**字段 3: check_in**
- Type: `Date`
- Name: `check_in`
- Label: `入住日期`
- ✅ Required

**字段 4: check_out**
- Type: `Date`
- Name: `check_out`
- Label: `退房日期`
- ✅ Required

**字段 5: guests**
- Type: `Number`
- Name: `guests`
- Label: `入住人数`
- ✅ Required
- Min: `1`, Max: `10`

**字段 6: guest_names**
- Type: `JSON`
- Name: `guest_names`
- Label: `入住人员姓名`

**字段 7: purpose**
- Type: `Select (single)`
- Name: `purpose`
- Label: `住宿目的`
- ✅ Required
- Values: `business`, `training`, `visitor`, `other`

**字段 8: status**
- Type: `Select (single)`
- Name: `status`
- Label: `预订状态`
- ✅ Required
- Values: `pending`, `confirmed`, `checked_in`, `checked_out`, `cancelled`

**字段 9: total_amount**
- Type: `Number`
- Name: `total_amount`
- Label: `总金额`
- Min: `0`

**字段 10: paid_amount**
- Type: `Number`
- Name: `paid_amount`
- Label: `已支付金额`
- Min: `0`

**字段 11: notes**
- Type: `Text`
- Name: `notes`
- Label: `备注`
- Max length: `1000`

**字段 12: approved_by**
- Type: `Relation (single)`
- Name: `approved_by`
- Label: `审批人`
- Collection: `users`
- Display fields: `name`, `username`

**字段 13: approved_at**
- Type: `Date`
- Name: `approved_at`
- Label: `审批时间`

### 设置 bookings 权限规则：

点击 **"API Rules"** 标签页，设置：

- **List/Search rule**: 
  ```
  @request.auth.id = user || @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **View rule**: 
  ```
  @request.auth.id = user || @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **Create rule**: 
  ```
  @request.auth.id != ""
  ```

- **Update rule**: 
  ```
  (@request.auth.id = user && status = "pending") || @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **Delete rule**: 
  ```
  @request.auth.role = "admin"
  ```

## 第四步：创建 payments 集合（可选）

1. 点击 **"New collection"**
2. 填写：
   - Name: `payments`
   - Type: `Base collection`
3. 点击 **"Create"**

### 添加 payments 字段：

**字段 1: booking**
- Type: `Relation (single)`
- Name: `booking`
- Label: `关联预订`
- ✅ Required
- Collection: `bookings`
- ✅ Cascade delete

**字段 2: amount**
- Type: `Number`
- Name: `amount`
- Label: `支付金额`
- ✅ Required
- Min: `0`

**字段 3: payment_date**
- Type: `Date`
- Name: `payment_date`
- Label: `支付日期`
- ✅ Required

**字段 4: payment_method**
- Type: `Select (single)`
- Name: `payment_method`
- Label: `支付方式`
- ✅ Required
- Values: `cash`, `card`, `transfer`, `deduction`

**字段 5: status**
- Type: `Select (single)`
- Name: `status`
- Label: `支付状态`
- ✅ Required
- Values: `pending`, `completed`, `failed`, `refunded`

**字段 6: transaction_id**
- Type: `Text`
- Name: `transaction_id`
- Label: `交易号`
- Max length: `100`

**字段 7: notes**
- Type: `Text`
- Name: `notes`
- Label: `备注`
- Max length: `500`

**字段 8: processed_by**
- Type: `Relation (single)`
- Name: `processed_by`
- Label: `处理人`
- Collection: `users`
- Display fields: `name`, `username`

### 设置 payments 权限规则：

- **List/Search rule**: 
  ```
  @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **View rule**: 
  ```
  @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **Create rule**: 
  ```
  @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **Update rule**: 
  ```
  @request.auth.role = "admin" || @request.auth.role = "manager"
  ```

- **Delete rule**: 
  ```
  @request.auth.role = "admin"
  ```

## ✅ 验证完成

完成后，您应该看到：
- **Auth collections** 中的 `users` 集合有 7 个自定义字段
- **Collections** 中有 3 个集合：`rooms`, `bookings`, `payments`
- 每个集合都有正确的权限规则

## 🎯 下一步

1. 添加测试数据
2. 创建测试用户账户
3. 测试系统功能

## ❗ 重要提示

- 创建关系字段时，确保被引用的集合已经存在
- 权限规则中的字段名要与实际字段名完全一致
- 复制权限规则时注意不要有多余的空格或换行
