@echo off
echo ================================
echo Accommodation System Status Check
echo ================================

echo.
echo Checking PocketBase installation...

if exist "pocketbase.exe" (
    echo [OK] PocketBase executable found
) else (
    echo [ERROR] PocketBase executable not found
    echo Please run setup.bat first
    goto :end
)

echo.
echo Checking project structure...

if exist "pb_public" (
    echo [OK] pb_public directory exists
) else (
    echo [ERROR] pb_public directory missing
)

if exist "pb_public\index.html" (
    echo [OK] Main application file found
) else (
    echo [ERROR] index.html missing
)

if exist "pb_public\css\style.css" (
    echo [OK] CSS styles found
) else (
    echo [ERROR] CSS file missing
)

if exist "pb_public\js\app.js" (
    echo [OK] JavaScript application found
) else (
    echo [ERROR] JavaScript file missing
)

if exist "pb_public\js\auth.js" (
    echo [OK] Authentication module found
) else (
    echo [ERROR] Authentication file missing
)

echo.
echo Checking if PocketBase is running...
netstat -an | findstr ":8090" >nul
if %errorlevel% equ 0 (
    echo [OK] PocketBase is running on port 8090
    echo.
    echo Service URLs:
    echo - Admin Dashboard: http://127.0.0.1:8090/_/
    echo - Application: http://127.0.0.1:8090/
) else (
    echo [INFO] PocketBase is not running
    echo Run start.bat to start the service
)

echo.
echo ================================
echo Status Check Complete
echo ================================

:end
pause
