# 🚀 快速设置指南 - 创建数据集合

## 方法一：使用导入功能（推荐）

### 步骤 1：访问管理后台
1. 打开浏览器访问：http://127.0.0.1:8090/_/
2. 如果是首次访问，创建管理员账户：
   - Email: `<EMAIL>`
   - Password: `admin123`（测试用）

### 步骤 2：导入集合配置
1. 在管理后台左侧菜单，点击 **"Settings"**
2. 找到 **"Import collections"** 选项
3. 点击 **"Load from JSON file"** 或直接粘贴JSON内容
4. 复制 `collections-import.json` 文件的全部内容并粘贴
5. 点击 **"Review"** 检查配置
6. 点击 **"Import"** 完成导入

## 方法二：手动创建集合

### 步骤 1：创建 rooms 集合

1. 在管理后台左侧菜单，点击 **"Collections"**
2. 点击 **"New collection"** 按钮
3. 填写基本信息：
   - **Name**: `rooms`
   - **Type**: `Base collection`
4. 点击 **"Create"** 创建集合

5. 添加字段（点击 **"New field"** 按钮）：

   **字段 1: room_number**
   - Type: `Text`
   - Name: `room_number`
   - ✅ Required
   - ✅ Unique
   - Max length: 10

   **字段 2: room_type**
   - Type: `Select (single)`
   - Name: `room_type`
   - ✅ Required
   - Values: `single`, `double`, `suite`, `family`

   **字段 3: capacity**
   - Type: `Number`
   - Name: `capacity`
   - ✅ Required
   - Min: 1, Max: 10

   **字段 4: price**
   - Type: `Number`
   - Name: `price`
   - ✅ Required
   - Min: 0

   **字段 5: status**
   - Type: `Select (single)`
   - Name: `status`
   - ✅ Required
   - Values: `available`, `occupied`, `maintenance`, `reserved`

   **字段 6: description**
   - Type: `Text`
   - Name: `description`
   - Max length: 500

   **字段 7: amenities**
   - Type: `JSON`
   - Name: `amenities`

   **字段 8: floor**
   - Type: `Number`
   - Name: `floor`
   - Min: 1, Max: 50

   **字段 9: images**
   - Type: `File`
   - Name: `images`
   - Max select: 5
   - Max size: 5MB
   - Mime types: `image/jpeg`, `image/png`, `image/webp`

### 步骤 2：设置 rooms 集合权限

在 rooms 集合的 **"API Rules"** 标签页：

- **List/Search rule**: `@request.auth.id != ""`
- **View rule**: `@request.auth.id != ""`
- **Create rule**: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- **Update rule**: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- **Delete rule**: `@request.auth.role = "admin"`

### 步骤 3：配置用户集合字段

1. 在左侧菜单点击 **"Auth collections"**
2. 点击 **"users"** 集合
3. 在 **"Fields"** 标签页添加以下字段：

   **字段 1: name**
   - Type: `Text`
   - Name: `name`
   - Max length: 100

   **字段 2: employee_id**
   - Type: `Text`
   - Name: `employee_id`
   - ✅ Unique
   - Max length: 20

   **字段 3: department**
   - Type: `Text`
   - Name: `department`
   - Max length: 100

   **字段 4: position**
   - Type: `Text`
   - Name: `position`
   - Max length: 100

   **字段 5: phone**
   - Type: `Text`
   - Name: `phone`
   - Max length: 20

   **字段 6: role**
   - Type: `Select (single)`
   - Name: `role`
   - ✅ Required
   - Values: `admin`, `manager`, `employee`

   **字段 7: is_active**
   - Type: `Bool`
   - Name: `is_active`
   - Default: `true`

### 步骤 4：创建 bookings 集合

1. 创建新集合：
   - **Name**: `bookings`
   - **Type**: `Base collection`

2. 添加字段：

   **字段 1: user**
   - Type: `Relation (single)`
   - Name: `user`
   - ✅ Required
   - Collection: `users`
   - Display fields: `name`, `username`

   **字段 2: room**
   - Type: `Relation (single)`
   - Name: `room`
   - ✅ Required
   - Collection: `rooms`
   - Display fields: `room_number`, `room_type`

   **字段 3: check_in**
   - Type: `Date`
   - Name: `check_in`
   - ✅ Required

   **字段 4: check_out**
   - Type: `Date`
   - Name: `check_out`
   - ✅ Required

   **字段 5: guests**
   - Type: `Number`
   - Name: `guests`
   - ✅ Required
   - Min: 1, Max: 10

   **字段 6: guest_names**
   - Type: `JSON`
   - Name: `guest_names`

   **字段 7: purpose**
   - Type: `Select (single)`
   - Name: `purpose`
   - ✅ Required
   - Values: `business`, `training`, `visitor`, `other`

   **字段 8: status**
   - Type: `Select (single)`
   - Name: `status`
   - ✅ Required
   - Values: `pending`, `confirmed`, `checked_in`, `checked_out`, `cancelled`

   **字段 9: total_amount**
   - Type: `Number`
   - Name: `total_amount`
   - Min: 0

   **字段 10: paid_amount**
   - Type: `Number`
   - Name: `paid_amount`
   - Min: 0

   **字段 11: notes**
   - Type: `Text`
   - Name: `notes`
   - Max length: 1000

   **字段 12: approved_by**
   - Type: `Relation (single)`
   - Name: `approved_by`
   - Collection: `users`
   - Display fields: `name`, `username`

   **字段 13: approved_at**
   - Type: `Date`
   - Name: `approved_at`

### 步骤 5：设置 bookings 集合权限

在 bookings 集合的 **"API Rules"** 标签页：

- **List/Search rule**: `@request.auth.id = user || @request.auth.role = "admin" || @request.auth.role = "manager"`
- **View rule**: `@request.auth.id = user || @request.auth.role = "admin" || @request.auth.role = "manager"`
- **Create rule**: `@request.auth.id != ""`
- **Update rule**: `(@request.auth.id = user && status = "pending") || @request.auth.role = "admin" || @request.auth.role = "manager"`
- **Delete rule**: `@request.auth.role = "admin"`

### 步骤 6：创建 payments 集合（可选）

1. 创建新集合：
   - **Name**: `payments`
   - **Type**: `Base collection`

2. 添加字段：

   **字段 1: booking**
   - Type: `Relation (single)`
   - Name: `booking`
   - ✅ Required
   - Collection: `bookings`
   - ✅ Cascade delete

   **字段 2: amount**
   - Type: `Number`
   - Name: `amount`
   - ✅ Required
   - Min: 0

   **字段 3: payment_date**
   - Type: `Date`
   - Name: `payment_date`
   - ✅ Required

   **字段 4: payment_method**
   - Type: `Select (single)`
   - Name: `payment_method`
   - ✅ Required
   - Values: `cash`, `card`, `transfer`, `deduction`

   **字段 5: status**
   - Type: `Select (single)`
   - Name: `status`
   - ✅ Required
   - Values: `pending`, `completed`, `failed`, `refunded`

   **字段 6: transaction_id**
   - Type: `Text`
   - Name: `transaction_id`
   - Max length: 100

   **字段 7: notes**
   - Type: `Text`
   - Name: `notes`
   - Max length: 500

   **字段 8: processed_by**
   - Type: `Relation (single)`
   - Name: `processed_by`
   - Collection: `users`
   - Display fields: `name`, `username`

### 步骤 7：设置 payments 集合权限

在 payments 集合的 **"API Rules"** 标签页：

- **List/Search rule**: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- **View rule**: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- **Create rule**: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- **Update rule**: `@request.auth.role = "admin" || @request.auth.role = "manager"`
- **Delete rule**: `@request.auth.role = "admin"`

## ✅ 验证设置

完成后，您应该看到：
1. **Collections** 页面有 3 个集合：`rooms`, `bookings`, `payments`
2. **Auth collections** 页面的 `users` 集合有额外的自定义字段
3. 每个集合都有正确的权限规则设置

## 🎯 下一步

设置完成后，您可以：
1. 添加测试数据（参考 `test-data.json`）
2. 访问应用前端：http://127.0.0.1:8090/
3. 创建测试用户账户
4. 测试系统功能

## ❗ 常见问题

1. **关系字段显示错误**：确保先创建被引用的集合
2. **权限规则报错**：检查字段名称是否正确
3. **导入失败**：确保JSON格式正确，可以先创建一个集合再导入其他
