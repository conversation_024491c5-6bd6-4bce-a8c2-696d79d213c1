@echo off
echo ================================
echo Accommodation System - PocketBase Setup
echo ================================

echo.
echo Downloading PocketBase...

:: Create temp directory
if not exist "temp" mkdir temp

:: Download PocketBase
curl -L "https://github.com/pocketbase/pocketbase/releases/latest/download/pocketbase_windows_amd64.zip" -o "temp/pocketbase.zip"

if %errorlevel% neq 0 (
    echo Download failed, please check network connection
    pause
    exit /b 1
)

echo Extracting files...
powershell -command "Expand-Archive -Path 'temp/pocketbase.zip' -DestinationPath '.' -Force"

if %errorlevel% neq 0 (
    echo Extraction failed
    pause
    exit /b 1
)

:: Clean up temp files
rmdir /s /q temp

echo.
echo Creating project directory structure...

:: Create necessary directories
if not exist "pb_public" mkdir pb_public
if not exist "pb_public\css" mkdir pb_public\css
if not exist "pb_public\js" mkdir pb_public\js
if not exist "pb_public\assets" mkdir pb_public\assets

echo.
echo ================================
echo Installation Complete!
echo ================================
echo.
echo Usage Instructions:
echo 1. Run start.bat to start the service
echo 2. Visit http://127.0.0.1:8090/_/ for admin dashboard
echo 3. Create admin account on first visit
echo.
pause
