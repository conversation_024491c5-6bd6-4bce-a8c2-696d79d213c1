[{"name": "rooms", "type": "base", "system": false, "schema": [{"name": "room_number", "type": "text", "system": false, "required": true, "unique": true, "options": {"min": 1, "max": 10, "pattern": ""}}, {"name": "room_type", "type": "select", "system": false, "required": true, "options": {"maxSelect": 1, "values": ["single", "double", "suite", "family"]}}, {"name": "capacity", "type": "number", "system": false, "required": true, "options": {"min": 1, "max": 10}}, {"name": "price", "type": "number", "system": false, "required": true, "options": {"min": 0}}, {"name": "status", "type": "select", "system": false, "required": true, "options": {"maxSelect": 1, "values": ["available", "occupied", "maintenance", "reserved"]}}, {"name": "description", "type": "text", "system": false, "required": false, "options": {"min": 0, "max": 500}}, {"name": "amenities", "type": "json", "system": false, "required": false}, {"name": "floor", "type": "number", "system": false, "required": false, "options": {"min": 1, "max": 50}}, {"name": "images", "type": "file", "system": false, "required": false, "options": {"maxSelect": 5, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/webp"], "thumbs": ["100x100", "300x300"]}}], "listRule": "@request.auth.id != ''", "viewRule": "@request.auth.id != ''", "createRule": "@request.auth.role = 'admin' || @request.auth.role = 'manager'", "updateRule": "@request.auth.role = 'admin' || @request.auth.role = 'manager'", "deleteRule": "@request.auth.role = 'admin'"}, {"id": "bookings_collection", "name": "bookings", "type": "base", "system": false, "schema": [{"id": "user", "name": "user", "type": "relation", "system": false, "required": true, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name", "username"]}}, {"id": "room", "name": "room", "type": "relation", "system": false, "required": true, "options": {"collectionId": "rooms_collection", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["room_number", "room_type"]}}, {"id": "check_in", "name": "check_in", "type": "date", "system": false, "required": true}, {"id": "check_out", "name": "check_out", "type": "date", "system": false, "required": true}, {"id": "guests", "name": "guests", "type": "number", "system": false, "required": true, "options": {"min": 1, "max": 10}}, {"id": "guest_names", "name": "guest_names", "type": "json", "system": false, "required": false}, {"id": "purpose", "name": "purpose", "type": "select", "system": false, "required": true, "options": {"maxSelect": 1, "values": ["business", "training", "visitor", "other"]}}, {"id": "status", "name": "status", "type": "select", "system": false, "required": true, "options": {"maxSelect": 1, "values": ["pending", "confirmed", "checked_in", "checked_out", "cancelled"]}}, {"id": "total_amount", "name": "total_amount", "type": "number", "system": false, "required": false, "options": {"min": 0}}, {"id": "paid_amount", "name": "paid_amount", "type": "number", "system": false, "required": false, "options": {"min": 0}}, {"id": "notes", "name": "notes", "type": "text", "system": false, "required": false, "options": {"min": 0, "max": 1000}}, {"id": "approved_by", "name": "approved_by", "type": "relation", "system": false, "required": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name", "username"]}}, {"id": "approved_at", "name": "approved_at", "type": "date", "system": false, "required": false}], "indexes": ["CREATE INDEX idx_booking_user ON bookings (user)", "CREATE INDEX idx_booking_room ON bookings (room)", "CREATE INDEX idx_booking_dates ON bookings (check_in, check_out)", "CREATE INDEX idx_booking_status ON bookings (status)"], "listRule": "@request.auth.id = user || @request.auth.role = 'admin' || @request.auth.role = 'manager'", "viewRule": "@request.auth.id = user || @request.auth.role = 'admin' || @request.auth.role = 'manager'", "createRule": "@request.auth.id != ''", "updateRule": "(@request.auth.id = user && status = 'pending') || @request.auth.role = 'admin' || @request.auth.role = 'manager'", "deleteRule": "@request.auth.role = 'admin'"}, {"id": "payments_collection", "name": "payments", "type": "base", "system": false, "schema": [{"id": "booking", "name": "booking", "type": "relation", "system": false, "required": true, "options": {"collectionId": "bookings_collection", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": ["id"]}}, {"id": "amount", "name": "amount", "type": "number", "system": false, "required": true, "options": {"min": 0}}, {"id": "payment_date", "name": "payment_date", "type": "date", "system": false, "required": true}, {"id": "payment_method", "name": "payment_method", "type": "select", "system": false, "required": true, "options": {"maxSelect": 1, "values": ["cash", "card", "transfer", "deduction"]}}, {"id": "status", "name": "status", "type": "select", "system": false, "required": true, "options": {"maxSelect": 1, "values": ["pending", "completed", "failed", "refunded"]}}, {"id": "transaction_id", "name": "transaction_id", "type": "text", "system": false, "required": false, "options": {"min": 0, "max": 100}}, {"id": "notes", "name": "notes", "type": "text", "system": false, "required": false, "options": {"min": 0, "max": 500}}, {"id": "processed_by", "name": "processed_by", "type": "relation", "system": false, "required": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name", "username"]}}], "indexes": ["CREATE INDEX idx_payment_booking ON payments (booking)", "CREATE INDEX idx_payment_date ON payments (payment_date)", "CREATE INDEX idx_payment_status ON payments (status)"], "listRule": "@request.auth.role = 'admin' || @request.auth.role = 'manager'", "viewRule": "@request.auth.role = 'admin' || @request.auth.role = 'manager'", "createRule": "@request.auth.role = 'admin' || @request.auth.role = 'manager'", "updateRule": "@request.auth.role = 'admin' || @request.auth.role = 'manager'", "deleteRule": "@request.auth.role = 'admin'"}]