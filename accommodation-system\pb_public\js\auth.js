// 认证管理模块
class AuthManager {
    constructor() {
        this.pb = new PocketBase('http://127.0.0.1:8090');
        this.currentUser = null;
        this.init();
    }

    // 初始化认证状态
    init() {
        // 检查是否已登录
        if (this.pb.authStore.isValid) {
            this.currentUser = this.pb.authStore.model;
            this.updateUI();
        }

        // 监听认证状态变化
        this.pb.authStore.onChange((token, model) => {
            this.currentUser = model;
            this.updateUI();
        });
    }

    // 用户登录
    async login(usernameOrEmail, password) {
        try {
            showLoading();
            
            // 尝试用户名登录
            let authData;
            try {
                authData = await this.pb.collection('users').authWithPassword(usernameOrEmail, password);
            } catch (error) {
                // 如果用户名登录失败，可能是因为输入的是邮箱
                throw error;
            }

            this.currentUser = authData.record;
            
            showMessage('登录成功！', 'success');
            closeModal('loginModal');
            
            return authData;
        } catch (error) {
            console.error('登录失败:', error);
            showMessage('登录失败：' + (error.message || '用户名或密码错误'), 'error');
            throw error;
        } finally {
            hideLoading();
        }
    }

    // 用户注册
    async register(userData) {
        try {
            showLoading();
            
            const data = {
                username: userData.username,
                email: userData.email,
                emailVisibility: true,
                password: userData.password,
                passwordConfirm: userData.passwordConfirm,
                name: userData.name,
                employee_id: userData.employee_id,
                department: userData.department,
                position: userData.position,
                phone: userData.phone,
                role: userData.role || 'employee',
                is_active: true
            };

            const record = await this.pb.collection('users').create(data);
            
            showMessage('注册成功！', 'success');
            return record;
        } catch (error) {
            console.error('注册失败:', error);
            showMessage('注册失败：' + (error.message || '请检查输入信息'), 'error');
            throw error;
        } finally {
            hideLoading();
        }
    }

    // 用户登出
    logout() {
        this.pb.authStore.clear();
        this.currentUser = null;
        showMessage('已退出登录', 'info');
        
        // 重定向到首页并显示登录状态
        showSection('dashboard');
    }

    // 检查用户权限
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        const userRole = this.currentUser.role;
        
        const permissions = {
            'admin': ['view_all', 'create_all', 'update_all', 'delete_all', 'manage_users', 'view_reports'],
            'manager': ['view_all', 'create_bookings', 'update_bookings', 'manage_rooms', 'view_reports'],
            'employee': ['view_own', 'create_bookings', 'update_own']
        };

        return permissions[userRole]?.includes(permission) || false;
    }

    // 检查是否为管理员
    isAdmin() {
        return this.currentUser?.role === 'admin';
    }

    // 检查是否为管理员或经理
    isManagerOrAdmin() {
        return ['admin', 'manager'].includes(this.currentUser?.role);
    }

    // 更新UI显示
    updateUI() {
        const loginBtn = document.getElementById('loginBtn');
        const userInfo = document.getElementById('userInfo');
        const userName = document.getElementById('userName');
        const usersNav = document.getElementById('usersNav');
        const reportsNav = document.getElementById('reportsNav');
        const addRoomBtn = document.getElementById('addRoomBtn');

        if (this.currentUser) {
            // 已登录状态
            loginBtn.style.display = 'none';
            userInfo.style.display = 'flex';
            userName.textContent = this.currentUser.name || this.currentUser.username;

            // 根据权限显示/隐藏功能
            if (this.isManagerOrAdmin()) {
                if (usersNav) usersNav.style.display = 'block';
                if (reportsNav) reportsNav.style.display = 'block';
                if (addRoomBtn) addRoomBtn.style.display = 'inline-flex';
            }
        } else {
            // 未登录状态
            loginBtn.style.display = 'inline-flex';
            userInfo.style.display = 'none';
            
            // 隐藏需要权限的功能
            if (usersNav) usersNav.style.display = 'none';
            if (reportsNav) reportsNav.style.display = 'none';
            if (addRoomBtn) addRoomBtn.style.display = 'none';
        }
    }

    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }

    // 更新用户信息
    async updateProfile(userData) {
        try {
            showLoading();
            
            const record = await this.pb.collection('users').update(this.currentUser.id, userData);
            this.currentUser = record;
            
            showMessage('个人信息更新成功！', 'success');
            return record;
        } catch (error) {
            console.error('更新失败:', error);
            showMessage('更新失败：' + (error.message || '请检查输入信息'), 'error');
            throw error;
        } finally {
            hideLoading();
        }
    }

    // 修改密码
    async changePassword(oldPassword, newPassword) {
        try {
            showLoading();
            
            // 先验证旧密码
            await this.pb.collection('users').authWithPassword(
                this.currentUser.username, 
                oldPassword
            );
            
            // 更新密码
            await this.pb.collection('users').update(this.currentUser.id, {
                password: newPassword,
                passwordConfirm: newPassword
            });
            
            showMessage('密码修改成功！', 'success');
        } catch (error) {
            console.error('密码修改失败:', error);
            showMessage('密码修改失败：' + (error.message || '旧密码错误'), 'error');
            throw error;
        } finally {
            hideLoading();
        }
    }
}

// 全局认证管理器实例
const authManager = new AuthManager();

// 全局函数
function showLoginModal() {
    document.getElementById('loginModal').style.display = 'block';
}

function logout() {
    authManager.logout();
}

// 登录表单处理
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                await authManager.login(username, password);
                // 登录成功后刷新当前页面数据
                if (window.app && window.app.loadCurrentSectionData) {
                    window.app.loadCurrentSectionData();
                }
            } catch (error) {
                // 错误已在 AuthManager 中处理
            }
        });
    }
});

// 导出认证管理器供其他模块使用
window.authManager = authManager;
