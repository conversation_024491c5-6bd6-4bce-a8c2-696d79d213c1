# 数据库模型设计

## 数据表结构

### 1. rooms (房间表)

**字段定义：**
```javascript
{
  "id": "string",           // 自动生成的唯一ID
  "room_number": "string",  // 房间号 (唯一)
  "room_type": "select",    // 房型: single, double, suite, family
  "capacity": "number",     // 容纳人数
  "price": "number",        // 每晚价格
  "status": "select",       // 状态: available, occupied, maintenance, reserved
  "description": "text",    // 房间描述
  "amenities": "json",      // 设施列表 ["wifi", "tv", "ac", "bathroom"]
  "floor": "number",        // 楼层
  "images": "file",         // 房间图片 (多文件)
  "created": "datetime",    // 创建时间
  "updated": "datetime"     // 更新时间
}
```

**索引：**
- room_number (唯一索引)
- status
- room_type

### 2. users (用户表)

**字段定义：**
```javascript
{
  "id": "string",           // 自动生成的唯一ID
  "username": "string",     // 用户名 (唯一)
  "email": "email",         // 邮箱 (唯一)
  "emailVisibility": "bool", // 邮箱可见性
  "verified": "bool",       // 邮箱验证状态
  "name": "string",         // 真实姓名
  "employee_id": "string",  // 员工编号 (唯一)
  "department": "string",   // 部门
  "position": "string",     // 职位
  "phone": "string",        // 电话号码
  "role": "select",         // 角色: admin, manager, employee
  "avatar": "file",         // 头像
  "is_active": "bool",      // 账户状态
  "created": "datetime",    // 创建时间
  "updated": "datetime"     // 更新时间
}
```

**索引：**
- username (唯一索引)
- email (唯一索引)
- employee_id (唯一索引)
- department
- role

### 3. bookings (预订表)

**字段定义：**
```javascript
{
  "id": "string",           // 自动生成的唯一ID
  "user": "relation",       // 关联用户 (users表)
  "room": "relation",       // 关联房间 (rooms表)
  "check_in": "date",       // 入住日期
  "check_out": "date",      // 退房日期
  "guests": "number",       // 入住人数
  "guest_names": "json",    // 入住人员姓名列表
  "purpose": "select",      // 住宿目的: business, training, visitor, other
  "status": "select",       // 状态: pending, confirmed, checked_in, checked_out, cancelled
  "total_amount": "number", // 总金额
  "paid_amount": "number",  // 已支付金额
  "notes": "text",          // 备注
  "approved_by": "relation", // 审批人 (users表)
  "approved_at": "datetime", // 审批时间
  "created": "datetime",    // 创建时间
  "updated": "datetime"     // 更新时间
}
```

**索引：**
- user
- room
- check_in
- check_out
- status

### 4. payments (支付记录表)

**字段定义：**
```javascript
{
  "id": "string",           // 自动生成的唯一ID
  "booking": "relation",    // 关联预订 (bookings表)
  "amount": "number",       // 支付金额
  "payment_date": "datetime", // 支付日期
  "payment_method": "select", // 支付方式: cash, card, transfer, deduction
  "status": "select",       // 支付状态: pending, completed, failed, refunded
  "transaction_id": "string", // 交易号
  "notes": "text",          // 备注
  "processed_by": "relation", // 处理人 (users表)
  "created": "datetime",    // 创建时间
  "updated": "datetime"     // 更新时间
}
```

**索引：**
- booking
- payment_date
- status

### 5. room_maintenance (房间维护记录表)

**字段定义：**
```javascript
{
  "id": "string",           // 自动生成的唯一ID
  "room": "relation",       // 关联房间 (rooms表)
  "maintenance_type": "select", // 维护类型: cleaning, repair, upgrade, inspection
  "description": "text",    // 维护描述
  "start_date": "datetime", // 开始时间
  "end_date": "datetime",   // 结束时间
  "status": "select",       // 状态: scheduled, in_progress, completed, cancelled
  "cost": "number",         // 维护费用
  "assigned_to": "string",  // 负责人
  "notes": "text",          // 备注
  "created": "datetime",    // 创建时间
  "updated": "datetime"     // 更新时间
}
```

### 6. system_settings (系统设置表)

**字段定义：**
```javascript
{
  "id": "string",           // 自动生成的唯一ID
  "key": "string",          // 设置键名 (唯一)
  "value": "json",          // 设置值
  "description": "text",    // 设置描述
  "category": "string",     // 设置分类
  "created": "datetime",    // 创建时间
  "updated": "datetime"     // 更新时间
}
```

## 关系说明

1. **users → bookings**: 一对多关系，一个用户可以有多个预订
2. **rooms → bookings**: 一对多关系，一个房间可以有多个预订记录
3. **bookings → payments**: 一对多关系，一个预订可以有多个支付记录
4. **rooms → room_maintenance**: 一对多关系，一个房间可以有多个维护记录
5. **users → bookings (approved_by)**: 多对一关系，审批人关系
6. **users → payments (processed_by)**: 多对一关系，支付处理人关系

## 权限规则设计

### rooms 集合权限
- **查看**: 所有认证用户
- **创建**: admin, manager
- **更新**: admin, manager
- **删除**: admin

### users 集合权限
- **查看**: 自己的记录 + admin/manager 可查看所有
- **创建**: admin
- **更新**: 自己的基本信息 + admin 可更新所有
- **删除**: admin

### bookings 集合权限
- **查看**: 自己的预订 + admin/manager 可查看所有
- **创建**: 所有认证用户
- **更新**: 自己的预订(pending状态) + admin/manager 可更新所有
- **删除**: admin

### payments 集合权限
- **查看**: 相关预订的用户 + admin/manager
- **创建**: admin, manager
- **更新**: admin, manager
- **删除**: admin

## 业务规则

1. **房间预订冲突检查**: 同一房间在同一时间段不能被重复预订
2. **预订状态流转**: pending → confirmed → checked_in → checked_out
3. **支付状态关联**: 预订状态与支付状态的关联验证
4. **房间状态自动更新**: 根据预订情况自动更新房间状态
5. **权限验证**: 基于用户角色的操作权限控制
