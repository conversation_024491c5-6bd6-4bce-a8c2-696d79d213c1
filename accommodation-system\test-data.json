{"rooms": [{"room_number": "101", "room_type": "single", "capacity": 1, "price": 200, "status": "available", "description": "标准单人间，配备独立卫浴、空调、WiFi、办公桌", "amenities": ["wifi", "ac", "desk", "bathroom", "tv"], "floor": 1}, {"room_number": "102", "room_type": "single", "capacity": 1, "price": 200, "status": "occupied", "description": "标准单人间，配备独立卫浴、空调、WiFi、办公桌", "amenities": ["wifi", "ac", "desk", "bathroom", "tv"], "floor": 1}, {"room_number": "103", "room_type": "double", "capacity": 2, "price": 300, "status": "available", "description": "舒适双人间，适合商务出差，配备双床或大床", "amenities": ["wifi", "ac", "desk", "bathroom", "tv", "minibar"], "floor": 1}, {"room_number": "104", "room_type": "double", "capacity": 2, "price": 300, "status": "maintenance", "description": "舒适双人间，适合商务出差，配备双床或大床", "amenities": ["wifi", "ac", "desk", "bathroom", "tv", "minibar"], "floor": 1}, {"room_number": "201", "room_type": "suite", "capacity": 3, "price": 500, "status": "available", "description": "豪华套房，配备客厅、办公区、独立卧室", "amenities": ["wifi", "ac", "desk", "bathroom", "tv", "minibar", "living_room", "kitchenette"], "floor": 2}, {"room_number": "202", "room_type": "suite", "capacity": 3, "price": 500, "status": "reserved", "description": "豪华套房，配备客厅、办公区、独立卧室", "amenities": ["wifi", "ac", "desk", "bathroom", "tv", "minibar", "living_room", "kitchenette"], "floor": 2}, {"room_number": "301", "room_type": "family", "capacity": 4, "price": 600, "status": "available", "description": "家庭房，适合多人入住，配备多个床位", "amenities": ["wifi", "ac", "desk", "bathroom", "tv", "minibar", "multiple_beds"], "floor": 3}, {"room_number": "302", "room_type": "family", "capacity": 4, "price": 600, "status": "available", "description": "家庭房，适合多人入住，配备多个床位", "amenities": ["wifi", "ac", "desk", "bathroom", "tv", "minibar", "multiple_beds"], "floor": 3}], "users": [{"username": "admin", "email": "<EMAIL>", "name": "系统管理员", "employee_id": "EMP001", "department": "信息技术部", "position": "系统管理员", "phone": "13800138001", "role": "admin", "is_active": true}, {"username": "manager1", "email": "<EMAIL>", "name": "张经理", "employee_id": "EMP002", "department": "人力资源部", "position": "部门经理", "phone": "13800138002", "role": "manager", "is_active": true}, {"username": "manager2", "email": "<EMAIL>", "name": "李经理", "employee_id": "EMP003", "department": "财务部", "position": "财务经理", "phone": "13800138003", "role": "manager", "is_active": true}, {"username": "employee1", "email": "<EMAIL>", "name": "王小明", "employee_id": "EMP004", "department": "销售部", "position": "销售专员", "phone": "13800138004", "role": "employee", "is_active": true}, {"username": "employee2", "email": "<EMAIL>", "name": "刘小红", "employee_id": "EMP005", "department": "市场部", "position": "市场专员", "phone": "***********", "role": "employee", "is_active": true}, {"username": "employee3", "email": "<EMAIL>", "name": "陈小华", "employee_id": "EMP006", "department": "技术部", "position": "软件工程师", "phone": "***********", "role": "employee", "is_active": true}], "sample_bookings": [{"user_note": "使用 employee1 用户ID", "room_note": "使用 101 房间ID", "check_in": "2024-08-05", "check_out": "2024-08-07", "guests": 1, "guest_names": ["王小明"], "purpose": "business", "status": "confirmed", "total_amount": 400, "paid_amount": 400, "notes": "商务出差住宿"}, {"user_note": "使用 employee2 用户ID", "room_note": "使用 103 房间ID", "check_in": "2024-08-10", "check_out": "2024-08-12", "guests": 2, "guest_names": ["刘小红", "同事A"], "purpose": "training", "status": "pending", "total_amount": 600, "paid_amount": 0, "notes": "参加培训课程"}, {"user_note": "使用 employee3 用户ID", "room_note": "使用 201 房间ID", "check_in": "2024-08-15", "check_out": "2024-08-18", "guests": 1, "guest_names": ["陈小华"], "purpose": "business", "status": "checked_in", "total_amount": 1500, "paid_amount": 1500, "notes": "重要客户接待"}], "system_settings": [{"key": "company_name", "value": "示例公司", "description": "公司名称", "category": "basic"}, {"key": "default_check_in_time", "value": "14:00", "description": "默认入住时间", "category": "booking"}, {"key": "default_check_out_time", "value": "12:00", "description": "默认退房时间", "category": "booking"}, {"key": "advance_booking_days", "value": 30, "description": "最多提前预订天数", "category": "booking"}, {"key": "cancellation_hours", "value": 24, "description": "取消预订最少提前小时数", "category": "booking"}, {"key": "auto_approve_employee", "value": false, "description": "员工预订是否自动审批", "category": "approval"}, {"key": "require_manager_approval", "value": true, "description": "是否需要经理审批", "category": "approval"}], "default_passwords": {"admin": "admin123", "manager1": "manager123", "manager2": "manager123", "employee1": "employee123", "employee2": "employee123", "employee3": "employee123"}, "notes": {"setup_order": ["1. 首先创建 rooms 集合并添加房间数据", "2. 然后创建用户账户（在 Auth collections 中）", "3. 创建 bookings 集合", "4. 添加示例预订数据（需要替换用户ID和房间ID）", "5. 可选：创建 system_settings 集合用于系统配置"], "important": ["所有密码仅用于测试环境", "生产环境请使用强密码", "预订数据中的用户ID和房间ID需要根据实际创建的记录进行替换", "建议先创建少量测试数据验证功能正常"]}}