// 主应用类
class AccommodationApp {
    constructor() {
        this.pb = new PocketBase('http://127.0.0.1:8090');
        this.currentSection = 'dashboard';
        this.init();
    }

    // 初始化应用
    init() {
        this.setupNavigation();
        this.setupEventListeners();
        this.loadDashboardData();
    }

    // 设置导航
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                this.showSection(section);
            });
        });
    }

    // 设置事件监听器
    setupEventListeners() {
        // 房间类型过滤器
        const roomTypeFilter = document.getElementById('roomTypeFilter');
        if (roomTypeFilter) {
            roomTypeFilter.addEventListener('change', () => this.loadRooms());
        }

        // 房间状态过滤器
        const roomStatusFilter = document.getElementById('roomStatusFilter');
        if (roomStatusFilter) {
            roomStatusFilter.addEventListener('change', () => this.loadRooms());
        }

        // 预订状态过滤器
        const bookingStatusFilter = document.getElementById('bookingStatusFilter');
        if (bookingStatusFilter) {
            bookingStatusFilter.addEventListener('change', () => this.loadBookings());
        }

        // 预订日期过滤器
        const bookingDateFilter = document.getElementById('bookingDateFilter');
        if (bookingDateFilter) {
            bookingDateFilter.addEventListener('change', () => this.loadBookings());
        }
    }

    // 显示指定部分
    showSection(sectionName) {
        // 隐藏所有部分
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => section.classList.remove('active'));

        // 显示指定部分
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // 更新导航状态
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-section') === sectionName) {
                link.classList.add('active');
            }
        });

        this.currentSection = sectionName;

        // 加载对应数据
        this.loadCurrentSectionData();
    }

    // 加载当前部分的数据
    loadCurrentSectionData() {
        switch (this.currentSection) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'rooms':
                this.loadRooms();
                break;
            case 'bookings':
                this.loadBookings();
                break;
            case 'users':
                if (authManager.isManagerOrAdmin()) {
                    this.loadUsers();
                }
                break;
            case 'reports':
                if (authManager.isManagerOrAdmin()) {
                    this.loadReports();
                }
                break;
        }
    }

    // 加载仪表板数据
    async loadDashboardData() {
        try {
            showLoading();

            // 并行加载统计数据
            const [rooms, bookings, users] = await Promise.all([
                this.pb.collection('rooms').getList(1, 1, { fields: 'id' }),
                this.pb.collection('bookings').getList(1, 1, { 
                    filter: 'status != "cancelled" && status != "checked_out"',
                    fields: 'id' 
                }),
                authManager.isManagerOrAdmin() ? 
                    this.pb.collection('users').getList(1, 1, { fields: 'id' }) : 
                    { totalItems: 0 }
            ]);

            // 获取可用房间数
            const availableRooms = await this.pb.collection('rooms').getList(1, 1, {
                filter: 'status = "available"',
                fields: 'id'
            });

            // 更新统计卡片
            document.getElementById('totalRooms').textContent = rooms.totalItems;
            document.getElementById('availableRooms').textContent = availableRooms.totalItems;
            document.getElementById('activeBookings').textContent = bookings.totalItems;
            document.getElementById('totalUsers').textContent = users.totalItems;

            // 加载最近预订
            await this.loadRecentBookings();

        } catch (error) {
            console.error('加载仪表板数据失败:', error);
            showMessage('加载数据失败', 'error');
        } finally {
            hideLoading();
        }
    }

    // 加载最近预订
    async loadRecentBookings() {
        try {
            const filter = authManager.isManagerOrAdmin() ? 
                '' : `user = "${authManager.getCurrentUser()?.id}"`;

            const bookings = await this.pb.collection('bookings').getList(1, 5, {
                sort: '-created',
                expand: 'user,room',
                filter: filter
            });

            const tbody = document.querySelector('#recentBookingsTable tbody');
            tbody.innerHTML = '';

            bookings.items.forEach(booking => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${booking.id.substring(0, 8)}</td>
                    <td>${booking.expand?.user?.name || '未知用户'}</td>
                    <td>${booking.expand?.room?.room_number || '未知房间'}</td>
                    <td>${this.formatDate(booking.check_in)}</td>
                    <td><span class="booking-status status-${booking.status}">${this.getStatusText(booking.status)}</span></td>
                `;
                tbody.appendChild(row);
            });

        } catch (error) {
            console.error('加载最近预订失败:', error);
        }
    }

    // 加载房间数据
    async loadRooms() {
        try {
            showLoading();

            let filter = '';
            const typeFilter = document.getElementById('roomTypeFilter')?.value;
            const statusFilter = document.getElementById('roomStatusFilter')?.value;

            const filters = [];
            if (typeFilter) filters.push(`room_type = "${typeFilter}"`);
            if (statusFilter) filters.push(`status = "${statusFilter}"`);
            
            if (filters.length > 0) {
                filter = filters.join(' && ');
            }

            const rooms = await this.pb.collection('rooms').getList(1, 50, {
                sort: 'room_number',
                filter: filter
            });

            this.renderRooms(rooms.items);

        } catch (error) {
            console.error('加载房间数据失败:', error);
            showMessage('加载房间数据失败', 'error');
        } finally {
            hideLoading();
        }
    }

    // 渲染房间列表
    renderRooms(rooms) {
        const roomsGrid = document.getElementById('roomsGrid');
        roomsGrid.innerHTML = '';

        rooms.forEach(room => {
            const roomCard = document.createElement('div');
            roomCard.className = 'room-card';
            roomCard.innerHTML = `
                <div class="room-header">
                    <div class="room-number">${room.room_number}</div>
                    <div class="room-type">${this.getRoomTypeText(room.room_type)}</div>
                </div>
                <div class="room-body">
                    <div class="room-info">
                        <span>容纳人数: ${room.capacity}</span>
                        <span>¥${room.price}/晚</span>
                    </div>
                    <div class="room-info">
                        <span>楼层: ${room.floor || '未设置'}</span>
                        <span class="room-status status-${room.status}">${this.getStatusText(room.status)}</span>
                    </div>
                    ${room.description ? `<p class="room-description">${room.description}</p>` : ''}
                    <div class="room-actions">
                        ${room.status === 'available' ? 
                            `<button class="btn-primary" onclick="app.bookRoom('${room.id}')">
                                <i class="fas fa-calendar-plus"></i> 预订
                            </button>` : ''
                        }
                        ${authManager.isManagerOrAdmin() ? 
                            `<button class="btn-secondary" onclick="app.editRoom('${room.id}')">
                                <i class="fas fa-edit"></i> 编辑
                            </button>` : ''
                        }
                    </div>
                </div>
            `;
            roomsGrid.appendChild(roomCard);
        });
    }

    // 获取房型文本
    getRoomTypeText(type) {
        const types = {
            'single': '单人间',
            'double': '双人间',
            'suite': '套房',
            'family': '家庭房'
        };
        return types[type] || type;
    }

    // 获取状态文本
    getStatusText(status) {
        const statuses = {
            'available': '可用',
            'occupied': '已入住',
            'maintenance': '维护中',
            'reserved': '已预订',
            'pending': '待确认',
            'confirmed': '已确认',
            'checked_in': '已入住',
            'checked_out': '已退房',
            'cancelled': '已取消'
        };
        return statuses[status] || status;
    }

    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    // 格式化日期时间
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    // 加载预订数据
    async loadBookings() {
        try {
            showLoading();

            let filter = '';
            const statusFilter = document.getElementById('bookingStatusFilter')?.value;
            const dateFilter = document.getElementById('bookingDateFilter')?.value;

            const filters = [];

            // 权限过滤
            if (!authManager.isManagerOrAdmin()) {
                filters.push(`user = "${authManager.getCurrentUser()?.id}"`);
            }

            if (statusFilter) filters.push(`status = "${statusFilter}"`);
            if (dateFilter) filters.push(`check_in >= "${dateFilter}"`);

            if (filters.length > 0) {
                filter = filters.join(' && ');
            }

            const bookings = await this.pb.collection('bookings').getList(1, 50, {
                sort: '-created',
                expand: 'user,room',
                filter: filter
            });

            this.renderBookings(bookings.items);

        } catch (error) {
            console.error('加载预订数据失败:', error);
            showMessage('加载预订数据失败', 'error');
        } finally {
            hideLoading();
        }
    }

    // 渲染预订列表
    renderBookings(bookings) {
        const tbody = document.querySelector('#bookingsTable tbody');
        tbody.innerHTML = '';

        bookings.forEach(booking => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${booking.id.substring(0, 8)}</td>
                <td>${booking.expand?.user?.name || '未知用户'}</td>
                <td>${booking.expand?.room?.room_number || '未知房间'}</td>
                <td>${this.formatDate(booking.check_in)}</td>
                <td>${this.formatDate(booking.check_out)}</td>
                <td>${booking.guests}</td>
                <td><span class="booking-status status-${booking.status}">${this.getStatusText(booking.status)}</span></td>
                <td>
                    <div class="action-buttons">
                        ${this.getBookingActions(booking)}
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 获取预订操作按钮
    getBookingActions(booking) {
        const actions = [];
        const isOwner = booking.user === authManager.getCurrentUser()?.id;
        const isManager = authManager.isManagerOrAdmin();

        // 查看详情
        actions.push(`<button class="btn-secondary btn-sm" onclick="app.viewBooking('${booking.id}')">
            <i class="fas fa-eye"></i> 查看
        </button>`);

        // 根据状态和权限显示不同操作
        if (booking.status === 'pending') {
            if (isManager) {
                actions.push(`<button class="btn-success btn-sm" onclick="app.confirmBooking('${booking.id}')">
                    <i class="fas fa-check"></i> 确认
                </button>`);
            }
            if (isOwner || isManager) {
                actions.push(`<button class="btn-danger btn-sm" onclick="app.cancelBooking('${booking.id}')">
                    <i class="fas fa-times"></i> 取消
                </button>`);
            }
        } else if (booking.status === 'confirmed') {
            if (isManager) {
                actions.push(`<button class="btn-primary btn-sm" onclick="app.checkInBooking('${booking.id}')">
                    <i class="fas fa-sign-in-alt"></i> 入住
                </button>`);
            }
        } else if (booking.status === 'checked_in') {
            if (isManager) {
                actions.push(`<button class="btn-warning btn-sm" onclick="app.checkOutBooking('${booking.id}')">
                    <i class="fas fa-sign-out-alt"></i> 退房
                </button>`);
            }
        }

        return actions.join(' ');
    }

    // 预订房间
    async bookRoom(roomId) {
        if (!authManager.getCurrentUser()) {
            showMessage('请先登录', 'warning');
            showLoginModal();
            return;
        }

        // 显示预订表单模态框
        this.showBookingModal(roomId);
    }

    // 显示预订模态框
    showBookingModal(roomId = null) {
        // 这里应该创建预订表单模态框
        // 为了简化，我们先用一个简单的提示
        showMessage('预订功能开发中...', 'info');
    }

    // 确认预订
    async confirmBooking(bookingId) {
        try {
            await this.pb.collection('bookings').update(bookingId, {
                status: 'confirmed',
                approved_by: authManager.getCurrentUser().id,
                approved_at: new Date().toISOString()
            });

            showMessage('预订已确认', 'success');
            this.loadBookings();
        } catch (error) {
            console.error('确认预订失败:', error);
            showMessage('确认预订失败', 'error');
        }
    }

    // 取消预订
    async cancelBooking(bookingId) {
        if (!confirm('确定要取消这个预订吗？')) return;

        try {
            await this.pb.collection('bookings').update(bookingId, {
                status: 'cancelled'
            });

            showMessage('预订已取消', 'success');
            this.loadBookings();
        } catch (error) {
            console.error('取消预订失败:', error);
            showMessage('取消预订失败', 'error');
        }
    }

    // 办理入住
    async checkInBooking(bookingId) {
        try {
            await this.pb.collection('bookings').update(bookingId, {
                status: 'checked_in'
            });

            showMessage('入住办理成功', 'success');
            this.loadBookings();
            this.loadDashboardData(); // 更新仪表板数据
        } catch (error) {
            console.error('办理入住失败:', error);
            showMessage('办理入住失败', 'error');
        }
    }

    // 办理退房
    async checkOutBooking(bookingId) {
        try {
            await this.pb.collection('bookings').update(bookingId, {
                status: 'checked_out'
            });

            showMessage('退房办理成功', 'success');
            this.loadBookings();
            this.loadDashboardData(); // 更新仪表板数据
        } catch (error) {
            console.error('办理退房失败:', error);
            showMessage('办理退房失败', 'error');
        }
    }

    // 查看预订详情
    async viewBooking(bookingId) {
        try {
            const booking = await this.pb.collection('bookings').getOne(bookingId, {
                expand: 'user,room'
            });

            // 显示预订详情模态框
            this.showBookingDetailsModal(booking);
        } catch (error) {
            console.error('获取预订详情失败:', error);
            showMessage('获取预订详情失败', 'error');
        }
    }

    // 显示预订详情模态框
    showBookingDetailsModal(booking) {
        // 这里应该创建详情模态框
        // 为了简化，我们先用alert显示基本信息
        const details = `
预订详情:
预订号: ${booking.id}
用户: ${booking.expand?.user?.name}
房间: ${booking.expand?.room?.room_number}
入住日期: ${this.formatDate(booking.check_in)}
退房日期: ${this.formatDate(booking.check_out)}
人数: ${booking.guests}
状态: ${this.getStatusText(booking.status)}
总金额: ¥${booking.total_amount || 0}
        `;
        alert(details);
    }

    // 加载用户数据
    async loadUsers() {
        if (!authManager.isManagerOrAdmin()) {
            showMessage('权限不足', 'error');
            return;
        }

        try {
            showLoading();

            const users = await this.pb.collection('users').getList(1, 50, {
                sort: '-created'
            });

            this.renderUsers(users.items);

        } catch (error) {
            console.error('加载用户数据失败:', error);
            showMessage('加载用户数据失败', 'error');
        } finally {
            hideLoading();
        }
    }

    // 渲染用户列表
    renderUsers(users) {
        const tbody = document.querySelector('#usersTable tbody');
        tbody.innerHTML = '';

        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.username}</td>
                <td>${user.name || '未设置'}</td>
                <td>${user.email}</td>
                <td>${user.department || '未设置'}</td>
                <td><span class="role-badge role-${user.role}">${this.getRoleText(user.role)}</span></td>
                <td><span class="status-badge ${user.is_active ? 'active' : 'inactive'}">${user.is_active ? '活跃' : '禁用'}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-secondary btn-sm" onclick="app.editUser('${user.id}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        ${user.is_active ?
                            `<button class="btn-warning btn-sm" onclick="app.deactivateUser('${user.id}')">
                                <i class="fas fa-ban"></i> 禁用
                            </button>` :
                            `<button class="btn-success btn-sm" onclick="app.activateUser('${user.id}')">
                                <i class="fas fa-check"></i> 启用
                            </button>`
                        }
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 获取角色文本
    getRoleText(role) {
        const roles = {
            'admin': '管理员',
            'manager': '经理',
            'employee': '员工'
        };
        return roles[role] || role;
    }

    // 编辑用户
    editUser(userId) {
        showMessage('用户编辑功能开发中...', 'info');
    }

    // 禁用用户
    async deactivateUser(userId) {
        if (!confirm('确定要禁用这个用户吗？')) return;

        try {
            await this.pb.collection('users').update(userId, {
                is_active: false
            });

            showMessage('用户已禁用', 'success');
            this.loadUsers();
        } catch (error) {
            console.error('禁用用户失败:', error);
            showMessage('禁用用户失败', 'error');
        }
    }

    // 启用用户
    async activateUser(userId) {
        try {
            await this.pb.collection('users').update(userId, {
                is_active: true
            });

            showMessage('用户已启用', 'success');
            this.loadUsers();
        } catch (error) {
            console.error('启用用户失败:', error);
            showMessage('启用用户失败', 'error');
        }
    }

    // 加载报表数据
    async loadReports() {
        if (!authManager.isManagerOrAdmin()) {
            showMessage('权限不足', 'error');
            return;
        }

        showMessage('报表功能开发中...', 'info');
    }

    // 编辑房间
    editRoom(roomId) {
        showMessage('房间编辑功能开发中...', 'info');
    }
}

// 全局工具函数
function showLoading() {
    document.getElementById('loading').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

function showMessage(message, type = 'info') {
    const messageEl = document.getElementById('message');
    messageEl.textContent = message;
    messageEl.className = `message ${type}`;
    messageEl.classList.add('show');

    setTimeout(() => {
        messageEl.classList.remove('show');
    }, 3000);
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function showSection(sectionName) {
    if (window.app) {
        window.app.showSection(sectionName);
    }
}

// 全局函数供HTML调用
function showAddRoomModal() {
    showMessage('添加房间功能开发中...', 'info');
}

function showAddBookingModal() {
    if (window.app) {
        window.app.showBookingModal();
    }
}

function showAddUserModal() {
    showMessage('添加用户功能开发中...', 'info');
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    window.app = new AccommodationApp();

    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
});
