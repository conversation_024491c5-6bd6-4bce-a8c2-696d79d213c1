@echo off
echo ================================
echo 住宿管理系统 - PocketBase 安装脚本
echo ================================

echo.
echo 正在下载 PocketBase...

:: 创建临时目录
if not exist "temp" mkdir temp

:: 下载 PocketBase
curl -L "https://github.com/pocketbase/pocketbase/releases/latest/download/pocketbase_windows_amd64.zip" -o "temp/pocketbase.zip"

if %errorlevel% neq 0 (
    echo 下载失败，请检查网络连接
    pause
    exit /b 1
)

echo 正在解压文件...
powershell -command "Expand-Archive -Path 'temp/pocketbase.zip' -DestinationPath '.' -Force"

if %errorlevel% neq 0 (
    echo 解压失败
    pause
    exit /b 1
)

:: 清理临时文件
rmdir /s /q temp

echo.
echo 创建项目目录结构...

:: 创建必要的目录
if not exist "pb_public" mkdir pb_public
if not exist "pb_public\css" mkdir pb_public\css
if not exist "pb_public\js" mkdir pb_public\js
if not exist "pb_public\assets" mkdir pb_public\assets

echo.
echo ================================
echo 安装完成！
echo ================================
echo.
echo 使用说明：
echo 1. 运行 start.bat 启动服务
echo 2. 访问 http://127.0.0.1:8090/_/ 进入管理后台
echo 3. 首次访问需要创建管理员账户
echo.
pause
