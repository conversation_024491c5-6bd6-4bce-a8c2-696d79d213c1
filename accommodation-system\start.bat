@echo off
echo ================================
echo Starting Accommodation Management System
echo ================================

if not exist "pocketbase.exe" (
    echo Error: pocketbase.exe not found
    echo Please run setup.bat first to install
    pause
    exit /b 1
)

echo Starting PocketBase service...
echo.
echo Service URLs:
echo - Admin Dashboard: http://127.0.0.1:8090/_/
echo - Application: http://127.0.0.1:8090/
echo.
echo Press Ctrl+C to stop the service
echo.

pocketbase.exe serve
