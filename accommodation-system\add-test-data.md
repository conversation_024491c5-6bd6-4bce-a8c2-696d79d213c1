# 📊 添加测试数据指南

完成集合创建后，按以下步骤添加测试数据：

## 第一步：创建测试用户

1. 在管理后台，点击 **"Auth collections"** → **"users"**
2. 点击 **"New record"** 按钮
3. 依次创建以下用户：

### 管理员用户
- **Username**: `admin`
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Password confirm**: `admin123`
- **Name**: `系统管理员`
- **Employee ID**: `EMP001`
- **Department**: `信息技术部`
- **Position**: `系统管理员`
- **Phone**: `13800138001`
- **Role**: `admin`
- **Is active**: ✅

### 经理用户
- **Username**: `manager1`
- **Email**: `<EMAIL>`
- **Password**: `manager123`
- **Password confirm**: `manager123`
- **Name**: `张经理`
- **Employee ID**: `EMP002`
- **Department**: `人力资源部`
- **Position**: `部门经理`
- **Phone**: `13800138002`
- **Role**: `manager`
- **Is active**: ✅

### 普通员工
- **Username**: `employee1`
- **Email**: `<EMAIL>`
- **Password**: `employee123`
- **Password confirm**: `employee123`
- **Name**: `王小明`
- **Employee ID**: `EMP004`
- **Department**: `销售部`
- **Position**: `销售专员`
- **Phone**: `13800138004`
- **Role**: `employee`
- **Is active**: ✅

## 第二步：添加测试房间

1. 点击 **"Collections"** → **"rooms"**
2. 点击 **"New record"** 按钮
3. 依次添加以下房间：

### 房间 101
- **Room number**: `101`
- **Room type**: `single`
- **Capacity**: `1`
- **Price**: `200`
- **Status**: `available`
- **Description**: `标准单人间，配备独立卫浴、空调、WiFi、办公桌`
- **Amenities**: 
  ```json
  ["wifi", "ac", "desk", "bathroom", "tv"]
  ```
- **Floor**: `1`

### 房间 102
- **Room number**: `102`
- **Room type**: `single`
- **Capacity**: `1`
- **Price**: `200`
- **Status**: `occupied`
- **Description**: `标准单人间，配备独立卫浴、空调、WiFi、办公桌`
- **Amenities**: 
  ```json
  ["wifi", "ac", "desk", "bathroom", "tv"]
  ```
- **Floor**: `1`

### 房间 103
- **Room number**: `103`
- **Room type**: `double`
- **Capacity**: `2`
- **Price**: `300`
- **Status**: `available`
- **Description**: `舒适双人间，适合商务出差，配备双床或大床`
- **Amenities**: 
  ```json
  ["wifi", "ac", "desk", "bathroom", "tv", "minibar"]
  ```
- **Floor**: `1`

### 房间 201
- **Room number**: `201`
- **Room type**: `suite`
- **Capacity**: `3`
- **Price**: `500`
- **Status**: `available`
- **Description**: `豪华套房，配备客厅、办公区、独立卧室`
- **Amenities**: 
  ```json
  ["wifi", "ac", "desk", "bathroom", "tv", "minibar", "living_room", "kitchenette"]
  ```
- **Floor**: `2`

### 房间 301
- **Room number**: `301`
- **Room type**: `family`
- **Capacity**: `4`
- **Price**: `600`
- **Status**: `available`
- **Description**: `家庭房，适合多人入住，配备多个床位`
- **Amenities**: 
  ```json
  ["wifi", "ac", "desk", "bathroom", "tv", "minibar", "multiple_beds"]
  ```
- **Floor**: `3`

## 第三步：添加测试预订

1. 点击 **"Collections"** → **"bookings"**
2. 点击 **"New record"** 按钮
3. 添加以下预订：

### 预订 1
- **User**: 选择 `王小明 (employee1)`
- **Room**: 选择 `101 (single)`
- **Check in**: `2024-08-05`
- **Check out**: `2024-08-07`
- **Guests**: `1`
- **Guest names**: 
  ```json
  ["王小明"]
  ```
- **Purpose**: `business`
- **Status**: `confirmed`
- **Total amount**: `400`
- **Paid amount**: `400`
- **Notes**: `商务出差住宿`

### 预订 2
- **User**: 选择其他用户
- **Room**: 选择 `103 (double)`
- **Check in**: `2024-08-10`
- **Check out**: `2024-08-12`
- **Guests**: `2`
- **Guest names**: 
  ```json
  ["刘小红", "同事A"]
  ```
- **Purpose**: `training`
- **Status**: `pending`
- **Total amount**: `600`
- **Paid amount**: `0`
- **Notes**: `参加培训课程`

## 第四步：测试系统功能

### 1. 测试前端访问
访问：http://127.0.0.1:8090/

### 2. 测试用户登录
使用创建的测试账户登录：
- 管理员：`<EMAIL>` / `admin123`
- 经理：`<EMAIL>` / `manager123`
- 员工：`<EMAIL>` / `employee123`

### 3. 验证功能
- ✅ 仪表板数据显示
- ✅ 房间列表和筛选
- ✅ 预订列表和管理
- ✅ 用户权限控制
- ✅ 数据统计更新

## 🔧 常见问题解决

### 1. 关系字段显示为空
- 确保被引用的记录已经存在
- 检查 Display fields 设置是否正确

### 2. 权限错误
- 检查用户的 role 字段是否正确设置
- 确认权限规则中的字段名拼写正确

### 3. JSON 字段格式错误
- 确保 JSON 格式正确，使用双引号
- 可以使用在线 JSON 验证工具检查格式

### 4. 前端无法加载数据
- 检查浏览器控制台是否有错误
- 确认 PocketBase 服务正在运行
- 验证集合名称和字段名称是否匹配

## ✅ 验证清单

完成后检查：
- [ ] 至少创建了 3 个测试用户（不同角色）
- [ ] 至少创建了 5 个测试房间（不同类型和状态）
- [ ] 至少创建了 2 个测试预订（不同状态）
- [ ] 前端页面可以正常访问
- [ ] 用户可以正常登录
- [ ] 数据在前端正确显示
- [ ] 权限控制正常工作

完成这些步骤后，您的住宿管理系统就可以正常使用了！
