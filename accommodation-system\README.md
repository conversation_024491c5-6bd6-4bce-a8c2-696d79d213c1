# 内部住宿管理系统

基于 PocketBase 构建的轻量化内部住宿管理系统，适用于企业内部员工住宿管理。

## 系统特性

- 🏠 房间管理：房型、价格、状态管理
- 👥 用户管理：员工信息、权限控制
- 📅 预订管理：预订申请、审批流程
- 💰 财务管理：费用计算、账单生成
- 📊 报表统计：入住率、收入分析
- 🔐 权限控制：基于角色的访问控制
- 📱 响应式设计：支持移动端访问

## 技术栈

- **后端**: PocketBase (Go)
- **前端**: HTML5 + CSS3 + JavaScript (Vanilla)
- **数据库**: SQLite (内置)
- **认证**: PocketBase Auth
- **部署**: 单文件部署

## 快速开始

### 1. 下载 PocketBase

```bash
# Windows
curl -L https://github.com/pocketbase/pocketbase/releases/latest/download/pocketbase_windows_amd64.zip -o pocketbase.zip
unzip pocketbase.zip

# macOS
curl -L https://github.com/pocketbase/pocketbase/releases/latest/download/pocketbase_darwin_amd64.zip -o pocketbase.zip
unzip pocketbase.zip

# Linux
curl -L https://github.com/pocketbase/pocketbase/releases/latest/download/pocketbase_linux_amd64.zip -o pocketbase.zip
unzip pocketbase.zip
```

### 2. 启动服务

```bash
./pocketbase serve
```

访问 http://127.0.0.1:8090/_/ 进入管理后台

### 3. 项目结构

```
accommodation-system/
├── pb_data/              # PocketBase 数据目录
├── pb_public/            # 静态文件目录
│   ├── index.html        # 主页面
│   ├── admin.html        # 管理页面
│   ├── css/
│   │   └── style.css     # 样式文件
│   ├── js/
│   │   ├── app.js        # 主应用逻辑
│   │   ├── admin.js      # 管理功能
│   │   └── auth.js       # 认证逻辑
│   └── assets/           # 静态资源
├── pocketbase*           # PocketBase 可执行文件
└── README.md
```

## 数据模型

### 房间表 (rooms)
- room_number: 房间号
- room_type: 房型 (single/double/suite)
- capacity: 容纳人数
- price: 每晚价格
- status: 状态 (available/occupied/maintenance)
- amenities: 设施描述

### 用户表 (users)
- username: 用户名
- email: 邮箱
- name: 姓名
- department: 部门
- role: 角色 (admin/manager/employee)
- phone: 电话

### 预订表 (bookings)
- user: 预订用户
- room: 预订房间
- check_in: 入住日期
- check_out: 退房日期
- guests: 入住人数
- status: 状态 (pending/confirmed/cancelled/completed)
- total_amount: 总金额
- notes: 备注

### 支付记录表 (payments)
- booking: 关联预订
- amount: 支付金额
- payment_date: 支付日期
- payment_method: 支付方式
- status: 支付状态

## 功能模块

### 1. 房间管理
- 房间信息维护
- 房间状态管理
- 房型配置

### 2. 预订管理
- 在线预订申请
- 预订审批流程
- 入住退房管理

### 3. 用户管理
- 员工信息管理
- 权限角色配置
- 部门管理

### 4. 财务管理
- 费用自动计算
- 账单生成
- 支付记录

### 5. 报表统计
- 入住率统计
- 收入分析
- 房间使用情况

## 部署说明

1. 将整个项目目录上传到服务器
2. 运行 `./pocketbase serve --http=0.0.0.0:8090`
3. 配置反向代理 (可选)
4. 设置 SSL 证书 (生产环境)

## 开发指南

详细的开发文档和 API 说明请参考各模块的具体实现文件。

## 许可证

MIT License
